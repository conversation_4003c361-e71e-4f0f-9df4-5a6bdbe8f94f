{"name": "livekit-agents-deepgram", "version": "1.0.0", "description": "LiveKit Agents + Deepgram 实时语音转录 - 企业级多人对话分类和多语言识别", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "agent": "node src/transcription-agent.js", "agent-simple": "node src/agent.js"}, "keywords": ["livekit", "deepgram", "speech-to-text", "transcription", "realtime"], "author": "", "license": "MIT", "dependencies": {"@livekit/agents": "^0.7.7", "@livekit/agents-plugin-deepgram": "^0.5.0", "@livekit/agents-plugin-openai": "^0.9.3", "@livekit/rtc-node": "^0.13.18", "dotenv": "^16.3.1", "express": "^4.18.2", "livekit-server-sdk": "^2.0.0"}, "devDependencies": {"@types/express": "^4.17.20", "@types/node": "^20.8.0", "@types/ws": "^8.5.8", "typescript": "^5.2.2"}}