#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 工作版本 Agent
 * 基于成功的架构，简化 STT 处理
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动工作版本转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动工作版本转录 Agent...");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 监听房间的轨道订阅事件
    ctx.room.on("trackSubscribed", async (track, publication, participant) => {
      console.log(`🎵 轨道订阅: ${track.kind} by ${participant.identity}`);

      if (track.kind === "audio" || track.kind === 1) {
        console.log(`🎧 开始处理音频轨道: ${participant.identity}`);

        try {
          // 创建 STT 流
          const sttStream = stt.stream();
          console.log(`🔍 STT Stream 已创建`);

          // 尝试使用 async iterator 处理 STT 输出
          (async () => {
            try {
              for await (const event of sttStream) {
                console.log(`📡 STT 事件: ${JSON.stringify(event)}`);

                if (event.text && event.text.trim()) {
                  console.log(
                    `📝 转录: ${event.text} (isFinal: ${event.isFinal})`
                  );

                  // 创建转录数据
                  const transcriptionData = {
                    type: "transcription",
                    text: event.text.trim(),
                    confidence: event.confidence || 0.9,
                    isFinal: event.isFinal || false,
                    timestamp: Date.now(),
                    participant: participant.identity,
                  };

                  // 广播转录结果到房间
                  try {
                    await ctx.room.localParticipant.publishData(
                      JSON.stringify(transcriptionData),
                      { reliable: true }
                    );
                    console.log(`✅ 转录结果已广播: ${event.text}`);
                  } catch (error) {
                    console.error(`❌ 广播转录结果失败: ${error.message}`);
                  }
                }
              }
            } catch (error) {
              console.error(`❌ STT 流处理错误: ${error.message}`);
            }
          })();

          // 尝试将音频轨道连接到 STT 流
          console.log(`🔗 尝试连接音频轨道到 STT...`);

          // 检查可用的方法
          console.log(`🔍 Track 方法: ${Object.getOwnPropertyNames(track)}`);
          console.log(
            `🔍 STT Stream 方法: ${Object.getOwnPropertyNames(sttStream)}`
          );

          // 使用正确的 LiveKit Agents API
          if (sttStream.input) {
            console.log(`🔗 使用 sttStream.input 连接音频轨道...`);

            // 尝试不同的输入方法
            if (typeof sttStream.input.pushTrack === "function") {
              sttStream.input.pushTrack(track);
              console.log(`✅ 使用 input.pushTrack 连接成功`);
            } else if (typeof sttStream.input.push === "function") {
              sttStream.input.push(track);
              console.log(`✅ 使用 input.push 连接成功`);
            } else {
              // 尝试直接赋值或其他方法
              console.log(
                `🔍 Input 对象方法: ${Object.getOwnPropertyNames(
                  sttStream.input
                )}`
              );

              // 尝试常见的方法名
              const commonMethods = [
                "write",
                "send",
                "feed",
                "connect",
                "connectTrack",
                "addTrack",
              ];
              let connected = false;

              for (const method of commonMethods) {
                if (typeof sttStream.input[method] === "function") {
                  console.log(`🎯 找到方法: ${method}`);
                  try {
                    sttStream.input[method](track);
                    console.log(`✅ 使用 input.${method} 连接成功`);
                    connected = true;
                    break;
                  } catch (error) {
                    console.log(`❌ input.${method} 失败: ${error.message}`);
                  }
                }
              }

              if (!connected) {
                console.log(`⚠️ 无法找到 input 连接方法`);
              }
            }
          } else {
            console.log(`❌ sttStream.input 不存在`);
          }
        } catch (error) {
          console.error(`❌ 处理音频轨道失败: ${error.message}`);
        }
      }
    });

    console.log("🎯 工作版本转录 Agent 已启动，等待音频输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
