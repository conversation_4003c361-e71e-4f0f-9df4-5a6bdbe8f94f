// LiveKit Agents - 最简单的测试代理

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import { fileURLToPath } from "node:url";
import { config } from "dotenv";

// 加载环境变量
config();

console.log("🔧 环境变量检查:");
console.log("LIVEKIT_URL:", process.env.LIVEKIT_URL);
console.log(
  "LIVEKIT_API_KEY:",
  process.env.LIVEKIT_API_KEY ? "已设置" : "未设置"
);

// 定义代理
export default defineAgent({
  entry: async (ctx) => {
    console.log("🎤 代理入口函数被调用");

    await ctx.connect();
    console.log("✅ 成功连接到 LiveKit 房间");

    console.log("🚀 代理已启动并准备就绪！");
  },
});

// 启动 Worker
console.log("🚀 启动 Worker...");
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
