#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 转录 Agent
 * 使用正确的 defineAgent 架构
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动 LiveKit Transcription Agent (最终版本)...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动转录 Agent...");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi", // 支持多语言
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true, // 说话人分离
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 监听房间的轨道订阅事件
    ctx.room.on("trackSubscribed", async (track, publication, participant) => {
      if (track.kind === "audio") {
        console.log(`🎵 开始处理音频轨道: ${participant.identity}`);

        try {
          // 使用 STT 处理音频轨道
          const sttStream = stt.stream();

          // 连接音频轨道到 STT
          sttStream.pushTrack(track);

          // 处理转录结果
          for await (const event of sttStream) {
            if (
              event.type === "transcript" &&
              event.text &&
              event.text.trim()
            ) {
              console.log(`📝 转录: ${event.text} (isFinal: ${event.isFinal})`);

              // 创建转录数据
              const transcriptionData = {
                type: "transcription",
                text: event.text.trim(),
                confidence: event.confidence || 0.9,
                isFinal: event.isFinal || false,
                timestamp: Date.now(),
                participant: participant.identity,
              };

              // 广播转录结果到房间
              try {
                await ctx.room.localParticipant.publishData(
                  JSON.stringify(transcriptionData),
                  { reliable: true }
                );
                console.log(`✅ 转录结果已广播: ${event.text}`);
              } catch (error) {
                console.error(`❌ 广播转录结果失败: ${error.message}`);
              }
            }
          }
        } catch (error) {
          console.error(`❌ 处理音频轨道失败: ${error.message}`);
        }
      }
    });

    console.log("🎯 转录 Agent 已启动，等待音频输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
