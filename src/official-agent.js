#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 官方架构 Agent
 * 基于官方文档的正确 AgentSession 实现
 */

import { WorkerOptions, cli, defineAgent, AgentSession, Agent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import * as openai from "@livekit/agents-plugin-openai";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动官方架构转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
  "OPENAI_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义转录助手类
class TranscriptionAssistant extends Agent {
  constructor() {
    super({
      instructions: "你是一个语音转录助手。你的主要任务是将用户的语音转换为文字，并将转录结果广播给所有参与者。请简短地确认你收到了用户的语音。"
    });
  }

  // 当用户完成一轮对话时调用
  async onUserTurnCompleted(turnCtx, newMessage) {
    console.log(`📝 用户语音转录完成: ${newMessage.textContent()}`);
    
    // 创建转录数据
    const transcriptionData = {
      type: "transcription",
      text: newMessage.textContent(),
      confidence: 0.95,
      isFinal: true,
      timestamp: Date.now(),
      participant: "user",
    };

    // 广播转录结果到房间
    try {
      await this.session.room.localParticipant.publishData(
        JSON.stringify(transcriptionData),
        { reliable: true }
      );
      console.log(`✅ 转录结果已广播: ${newMessage.textContent()}`);
    } catch (error) {
      console.error(`❌ 广播转录结果失败: ${error.message}`);
    }

    // 简短确认收到语音
    await this.session.say(`收到：${newMessage.textContent()}`, {
      allowInterruptions: true,
    });
  }
}

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动官方架构转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 AgentSession - 这是官方推荐的方法
    const session = new AgentSession({
      // STT 配置 - 使用 Deepgram
      stt: new deepgram.STT({
        model: "nova-2",
        language: "multi", // 支持多语言
        smartFormat: true,
        interimResults: true,
        punctuate: true,
        diarize: true,
      }),
      
      // LLM 配置 - 使用 OpenAI（用于处理转录结果）
      llm: new openai.LLM({
        model: "gpt-4o-mini",
      }),
      
      // 不使用 TTS，因为我们主要做转录
      // tts: null,
    });

    console.log("🎤 AgentSession 已配置 (STT: Deepgram, LLM: OpenAI)");

    // 启动会话 - 这会自动处理音频流连接
    await session.start({
      room: ctx.room,
      agent: new TranscriptionAssistant(),
    });

    console.log("✅ AgentSession 已启动，自动处理音频流");

    // 监听会话事件
    session.on("user_speech_committed", async (event) => {
      console.log(`📝 用户语音提交: ${event.text}`);
      
      // 创建转录数据
      const transcriptionData = {
        type: "transcription",
        text: event.text,
        confidence: event.confidence || 0.9,
        isFinal: true,
        timestamp: Date.now(),
        participant: participant.identity,
      };

      // 广播转录结果到房间
      try {
        await ctx.room.localParticipant.publishData(
          JSON.stringify(transcriptionData),
          { reliable: true }
        );
        console.log(`✅ 转录结果已广播: ${event.text}`);
      } catch (error) {
        console.error(`❌ 广播转录结果失败: ${error.message}`);
      }
    });

    // 监听中间转录结果
    session.on("user_speech_interim", async (event) => {
      if (event.text && event.text.trim()) {
        console.log(`📝 中间转录: ${event.text}`);
        
        // 创建中间转录数据
        const transcriptionData = {
          type: "transcription",
          text: event.text,
          confidence: event.confidence || 0.8,
          isFinal: false,
          timestamp: Date.now(),
          participant: participant.identity,
        };

        // 广播中间转录结果
        try {
          await ctx.room.localParticipant.publishData(
            JSON.stringify(transcriptionData),
            { reliable: true }
          );
          console.log(`📡 中间转录已广播: ${event.text}`);
        } catch (error) {
          console.error(`❌ 广播中间转录失败: ${error.message}`);
        }
      }
    });

    // 监听会话错误
    session.on("error", (error) => {
      console.error(`❌ AgentSession 错误: ${error.message}`);
    });

    // 发送初始问候
    await session.generateReply({
      instructions: "简短地问候用户，告诉他们你是语音转录助手，可以将他们的语音转换为文字。"
    });

    console.log("🎯 官方架构转录 Agent 已启动，等待语音输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
