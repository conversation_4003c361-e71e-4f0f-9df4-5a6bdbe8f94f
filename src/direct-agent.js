#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 直接处理 Agent
 * 使用最直接的方法处理音频数据
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动直接处理转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动直接处理转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 创建一个全局的 STT 流用于处理所有音频
    const globalSttStream = stt.stream();
    console.log("🌐 全局 STT Stream 已创建");

    // 处理 STT 输出
    (async () => {
      try {
        console.log(`🎧 开始监听全局 STT 输出...`);
        
        for await (const event of globalSttStream) {
          console.log(`📡 STT 事件: ${JSON.stringify(event)}`);
          
          if (event.text && event.text.trim()) {
            console.log(`📝 转录: ${event.text} (isFinal: ${event.isFinal})`);

            // 创建转录数据
            const transcriptionData = {
              type: "transcription",
              text: event.text.trim(),
              confidence: event.confidence || 0.9,
              isFinal: event.isFinal || false,
              timestamp: Date.now(),
              participant: participant.identity,
            };

            // 广播转录结果到房间
            try {
              await ctx.room.localParticipant.publishData(
                JSON.stringify(transcriptionData),
                { reliable: true }
              );
              console.log(`✅ 转录结果已广播: ${event.text}`);
            } catch (error) {
              console.error(`❌ 广播转录结果失败: ${error.message}`);
            }
          }
        }
      } catch (error) {
        console.error(`❌ 全局 STT 输出处理错误: ${error.message}`);
      }
    })();

    // 监听房间的轨道订阅事件
    ctx.room.on("trackSubscribed", async (track, publication, participant) => {
      console.log(`🎵 轨道订阅: ${track.kind} by ${participant.identity}`);
      
      if (track.kind === "audio" || track.kind === 1) {
        console.log(`🎧 开始处理音频轨道: ${participant.identity}`);
        
        try {
          // 尝试使用 LiveKit 的底层 API
          console.log(`🔍 探索轨道的底层 API...`);
          
          // 检查轨道的所有属性和方法
          const trackProps = Object.getOwnPropertyNames(track);
          const trackProto = Object.getOwnPropertyNames(Object.getPrototypeOf(track));
          console.log(`🔍 Track 属性: ${trackProps}`);
          console.log(`🔍 Track 原型方法: ${trackProto}`);
          
          // 尝试获取轨道信息
          if (track.info) {
            console.log(`📋 轨道信息: ${JSON.stringify(track.info)}`);
          }
          
          // 尝试使用 ffi_handle（这可能是 LiveKit 的底层接口）
          if (track.ffi_handle) {
            console.log(`🔧 轨道有 ffi_handle: ${typeof track.ffi_handle}`);
            
            // 尝试从 ffi_handle 获取音频数据
            try {
              console.log(`🎯 尝试从 ffi_handle 获取音频数据...`);
              
              // 这里我们需要找到正确的方法来获取音频数据
              // 由于这是底层 API，我们可能需要使用不同的方法
              
              // 尝试设置回调或监听器
              if (typeof track.ffi_handle.on === 'function') {
                track.ffi_handle.on('data', (audioData) => {
                  console.log(`🎵 从 ffi_handle 收到音频数据: ${audioData.length} bytes`);
                  
                  // 将音频数据发送到 STT
                  if (globalSttStream.input) {
                    // 尝试不同的方法发送数据
                    try {
                      globalSttStream.input.write(audioData);
                      console.log(`✅ 音频数据已发送到 STT`);
                    } catch (error) {
                      console.log(`❌ 发送音频数据失败: ${error.message}`);
                    }
                  }
                });
                console.log(`✅ ffi_handle 数据监听器已设置`);
              }
              
            } catch (error) {
              console.log(`❌ ffi_handle 处理失败: ${error.message}`);
            }
          }
          
          // 尝试模拟音频数据（用于测试）
          console.log(`🧪 发送测试音频数据到 STT...`);
          
          // 创建一个简单的测试，看看 STT 流是否工作
          setTimeout(() => {
            try {
              // 尝试发送一些测试数据到 STT 流
              if (globalSttStream.input) {
                console.log(`🧪 尝试发送测试数据...`);
                
                // 创建一个简单的音频缓冲区（静音）
                const testBuffer = Buffer.alloc(1024, 0);
                
                if (typeof globalSttStream.input.write === 'function') {
                  globalSttStream.input.write(testBuffer);
                  console.log(`✅ 测试数据已发送`);
                } else if (typeof globalSttStream.input.push === 'function') {
                  globalSttStream.input.push(testBuffer);
                  console.log(`✅ 测试数据已推送`);
                } else {
                  console.log(`❌ 无法发送测试数据`);
                }
              }
            } catch (error) {
              console.log(`❌ 测试数据发送失败: ${error.message}`);
            }
          }, 2000);

        } catch (error) {
          console.error(`❌ 处理音频轨道失败: ${error.message}`);
        }
      }
    });

    console.log("🎯 直接处理转录 Agent 已启动，等待音频输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
