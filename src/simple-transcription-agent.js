#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 简单转录 Agent
 * 基于官方示例的简化版本
 */

import { WorkerOptions, cli, defineAgent, multimodal } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动简单转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动简单转录 Agent...");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建一个简单的多模态 Agent，只使用 STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 创建多模态 Agent，配置音频参数
    const agent = new multimodal.MultimodalAgent({
      stt: stt,
      // 不使用 LLM 和 TTS，只做转录
      // 添加音频配置
      audioConfig: {
        sampleRate: 48000,
        channels: 1,
      },
    });

    console.log("🤖 多模态 Agent 已创建");

    // 启动 Agent
    const session = await agent.start(ctx.room, participant);

    console.log("✅ Agent 会话已启动");

    // 监听转录事件
    session.on("user_speech_committed", (event) => {
      console.log(`📝 用户语音转录: ${event.text}`);

      // 创建转录数据
      const transcriptionData = {
        type: "transcription",
        text: event.text,
        confidence: event.confidence || 0.9,
        isFinal: true,
        timestamp: Date.now(),
        participant: participant.identity,
      };

      // 广播转录结果到房间
      ctx.room.localParticipant
        .publishData(JSON.stringify(transcriptionData), { reliable: true })
        .then(() => {
          console.log(`✅ 转录结果已广播: ${event.text}`);
        })
        .catch((error) => {
          console.error(`❌ 广播转录结果失败: ${error.message}`);
        });
    });

    console.log("🎯 简单转录 Agent 已启动，等待语音输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
