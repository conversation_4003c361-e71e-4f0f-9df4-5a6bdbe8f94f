#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 会话 Agent
 * 基于官方文档但使用正确的 JS API
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import * as openai from "@livekit/agents-plugin-openai";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动会话转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
  "OPENAI_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动会话转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 创建 OpenAI LLM（用于处理转录结果）
    const llm = new openai.LLM({
      model: "gpt-4o-mini",
    });

    console.log("🧠 OpenAI LLM 已初始化");

    // 尝试使用 LiveKit Agents 的正确方式
    // 基于官方文档，我们应该能够创建一个会话来处理音频
    
    try {
      console.log("🔧 尝试创建 Agent 会话...");
      
      // 检查是否有 AgentSession 或类似的类
      console.log(`🔍 检查可用的 Agent 类...`);
      
      // 由于 LiveKit Agents JS 还在 beta，让我们尝试不同的方法
      // 基于官方文档的模式，我们应该能够处理音频流
      
      // 监听房间的轨道订阅事件
      ctx.room.on("trackSubscribed", async (track, publication, participant) => {
        console.log(`🎵 轨道订阅: ${track.kind} by ${participant.identity}`);
        
        if (track.kind === "audio" || track.kind === 1) {
          console.log(`🎧 开始处理音频轨道: ${participant.identity}`);
          
          try {
            // 使用官方推荐的方式：创建 STT 流并处理
            const sttStream = stt.stream();
            console.log("🔍 STT Stream 已创建");

            // 处理 STT 输出
            (async () => {
              try {
                console.log(`🎧 开始监听 STT 输出...`);
                
                for await (const event of sttStream) {
                  console.log(`📡 STT 事件: ${JSON.stringify(event)}`);
                  
                  if (event.text && event.text.trim()) {
                    console.log(`📝 转录: ${event.text} (isFinal: ${event.isFinal})`);

                    // 创建转录数据
                    const transcriptionData = {
                      type: "transcription",
                      text: event.text.trim(),
                      confidence: event.confidence || 0.9,
                      isFinal: event.isFinal || false,
                      timestamp: Date.now(),
                      participant: participant.identity,
                    };

                    // 广播转录结果到房间
                    try {
                      await ctx.room.localParticipant.publishData(
                        JSON.stringify(transcriptionData),
                        { reliable: true }
                      );
                      console.log(`✅ 转录结果已广播: ${event.text}`);
                    } catch (error) {
                      console.error(`❌ 广播转录结果失败: ${error.message}`);
                    }
                  }
                }
              } catch (error) {
                console.error(`❌ STT 输出处理错误: ${error.message}`);
              }
            })();

            // 基于官方文档，尝试使用正确的音频连接方法
            console.log(`🔗 尝试使用官方推荐的音频连接方法...`);
            
            // 方法1: 检查是否有 connectTrack 方法
            if (sttStream.connectTrack && typeof sttStream.connectTrack === 'function') {
              console.log(`✅ 使用 sttStream.connectTrack`);
              sttStream.connectTrack(track);
            }
            // 方法2: 检查 input 对象的连接方法
            else if (sttStream.input) {
              console.log(`🔗 尝试使用 sttStream.input 连接...`);
              
              // 尝试不同的连接方法
              const inputMethods = ['connectTrack', 'connect', 'addTrack', 'pushTrack'];
              let connected = false;
              
              for (const method of inputMethods) {
                if (typeof sttStream.input[method] === 'function') {
                  try {
                    console.log(`🎯 尝试 input.${method}...`);
                    await sttStream.input[method](track);
                    console.log(`✅ 使用 input.${method} 连接成功`);
                    connected = true;
                    break;
                  } catch (error) {
                    console.log(`❌ input.${method} 失败: ${error.message}`);
                  }
                }
              }
              
              if (!connected) {
                console.log(`⚠️ 无法找到 input 连接方法`);
                
                // 尝试直接赋值或其他方法
                try {
                  console.log(`🧪 尝试直接设置 input...`);
                  sttStream.input = track;
                  console.log(`✅ 直接设置 input 成功`);
                } catch (error) {
                  console.log(`❌ 直接设置 input 失败: ${error.message}`);
                }
              }
            }
            // 方法3: 尝试直接使用 STT 对象
            else {
              console.log(`🔗 尝试直接使用 STT 对象处理轨道...`);
              
              if (typeof stt.processTrack === 'function') {
                console.log(`✅ 使用 stt.processTrack`);
                await stt.processTrack(track);
              } else if (typeof stt.transcribeTrack === 'function') {
                console.log(`✅ 使用 stt.transcribeTrack`);
                await stt.transcribeTrack(track);
              } else {
                console.log(`❌ 无法找到处理轨道的方法`);
              }
            }

          } catch (error) {
            console.error(`❌ 处理音频轨道失败: ${error.message}`);
          }
        }
      });

      console.log("🎯 会话转录 Agent 已启动，等待音频输入...");
      
    } catch (error) {
      console.error(`❌ 创建 Agent 会话失败: ${error.message}`);
    }
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
