#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 转录 Agent
 * 使用正确的 defineAgent 架构
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动 LiveKit Transcription Agent (正确架构)...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent 类
class TranscriptionAgent extends Agent {
  constructor() {
    super({
      instructions: "你是一个实时语音转录助手，专门将用户的语音转换为文字。",
    });
  }

  // 当 Agent 进入会话时调用
  async onEnter() {
    console.log("🤖 Transcription Agent 进入会话");

    // 发送欢迎消息
    await this.session.say(
      "你好！我是语音转录助手，请开始说话，我会将你的语音转换为文字。",
      { allowInterruptions: false }
    );
  }

  // 当用户完成一轮对话时调用
  async onUserTurnCompleted(turnCtx, newMessage) {
    console.log(`📝 用户说话完成: ${newMessage.textContent()}`);

    // 创建转录数据
    const transcriptionData = {
      type: "transcription",
      text: newMessage.textContent(),
      timestamp: Date.now(),
      participant: "user",
      isFinal: true,
      confidence: 0.95,
    };

    // 广播转录结果到房间
    try {
      await this.session.room.localParticipant.publishData(
        JSON.stringify(transcriptionData),
        { reliable: true }
      );
      console.log(`✅ 转录结果已广播: ${newMessage.textContent()}`);
    } catch (error) {
      console.error(`❌ 广播转录结果失败: ${error.message}`);
    }

    // 可选：Agent 确认收到转录
    await this.session.say(`我听到了：${newMessage.textContent()}`, {
      allowInterruptions: true,
    });
  }
}

// 入口点函数
async function entrypoint(ctx) {
  console.log("🔧 启动 Agent Session...");
  console.log(`📍 房间: ${ctx.room.name}`);

  // 创建 Agent Session，配置 STT
  const session = new AgentSession({
    stt: new deepgram.STT({
      model: "nova-2",
      language: "multi", // 支持多语言
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true, // 说话人分离
    }),
    // 注意：这里不需要 LLM 和 TTS，因为我们只做转录
  });

  console.log("✅ Deepgram STT 已配置");

  // 启动会话
  await session.start({
    room: ctx.room,
    agent: new TranscriptionAgent(),
  });

  console.log("🎯 Agent Session 已启动，等待音频输入...");
}

// 启动 Agent
console.log("🔧 启动 LiveKit Worker...");

try {
  const workerOptions = new WorkerOptions({
    agent: fileURLToPath(import.meta.url),
    logLevel: "info",
  });

  cli.runApp(workerOptions);
} catch (error) {
  console.error("❌ Worker 启动失败:", error.message);
  process.exit(1);
}

export { entrypoint };
