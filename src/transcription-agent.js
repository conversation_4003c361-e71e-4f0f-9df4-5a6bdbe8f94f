#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 转录 Agent
 * 最小化实现版本
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

console.log("🚀 启动 LiveKit Transcription Agent...");

// 环境变量检查
const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  console.error("请检查 .env 文件配置");
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义转录 Agent
const transcriptionAgent = defineAgent({
  entry: async (ctx) => {
    console.log("🤖 Transcription Agent 启动");
    console.log(`📍 房间: ${ctx.room?.name || "unknown"}`);

    try {
      // 连接到房间
      await ctx.connect();
      console.log("✅ 已连接到 LiveKit 房间");

      // 等待参与者加入
      console.log("⏳ 等待参与者加入...");
      const participant = await ctx.waitForParticipant();
      console.log(`👤 参与者已加入: ${participant.identity}`);

      // 创建 Deepgram STT
      console.log("🎤 初始化 Deepgram STT...");
      const stt = new deepgram.STT({
        model: "nova-2",
        language: "multi",
        smartFormat: true,
        interimResults: true,
        punctuate: true,
        diarize: true,
      });

      console.log("✅ Deepgram STT 初始化完成");

      // 监听参与者连接事件
      ctx.room.on("participantConnected", (participant) => {
        console.log(`🔗 新参与者连接: ${participant.identity}`);
      });

      // 监听轨道发布事件
      ctx.room.on("trackPublished", async (publication, participant) => {
        console.log(
          `📡 轨道发布: ${publication.kind} by ${participant.identity}`
        );

        // 如果是音频轨道，主动订阅 (kind 可能是 "audio" 或 1)
        if (publication.kind === "audio" || publication.kind === 1) {
          console.log(`🔔 主动订阅音频轨道: ${participant.identity}`);
          try {
            await publication.setSubscribed(true);
            console.log(`✅ 音频轨道订阅成功: ${participant.identity}`);
          } catch (error) {
            console.error(`❌ 音频轨道订阅失败: ${error.message}`);
          }
        }
      });

      // 监听音频轨道订阅事件
      ctx.room.on(
        "trackSubscribed",
        async (track, publication, participant) => {
          if (track.kind === "audio" || track.kind === 1) {
            console.log(`🎵 处理音频轨道: ${participant.identity}`);

            try {
              // 使用正确的 API 获取音频流
              console.log(
                `🔍 轨道类型: ${typeof track}, 方法: ${Object.getOwnPropertyNames(
                  track
                )}`
              );
              console.log(
                `🔍 轨道信息: ${JSON.stringify(track.info, null, 2)}`
              );

              // 在 LiveKit Agents Node.js 中，需要使用不同的方法
              console.log(`🎧 开始音频流处理...`);

              // 使用 LiveKit Agents 的正确方式处理音频转录
              console.log(
                `🔍 STT 对象类型: ${typeof stt}, 方法: ${Object.getOwnPropertyNames(
                  stt
                )}`
              );

              // 尝试直接在 STT 对象上监听事件
              console.log(`🎯 尝试在 STT 对象上监听事件...`);

              stt.on("transcript", (result) => {
                console.log(`📝 STT 事件转录: ${JSON.stringify(result)}`);

                if (result.text && result.text.trim()) {
                  console.log(`📝 转录: ${result.text}`);

                  // 创建转录数据
                  const transcriptionData = {
                    type: "transcription",
                    text: result.text.trim(),
                    confidence: result.confidence || 0.9,
                    isFinal: result.isFinal || false,
                    timestamp: Date.now(),
                    participant: participant.identity,
                  };

                  // 广播转录结果
                  ctx.room.localParticipant
                    .publishData(JSON.stringify(transcriptionData), {
                      reliable: true,
                    })
                    .catch((err) => console.error("❌ 广播失败:", err.message));
                }
              });

              stt.on("error", (error) => {
                console.error(`❌ STT 对象错误: ${error.message}`);
              });

              const sttStream = stt.stream();
              console.log(
                `🔍 STT Stream 类型: ${typeof sttStream}, 方法: ${Object.getOwnPropertyNames(
                  sttStream
                )}`
              );

              // 处理转录结果 - 使用正确的事件监听方式
              if (sttStream.on) {
                sttStream.on("transcript", (result) => {
                  if (result.text && result.text.trim()) {
                    console.log(`📝 转录: ${result.text}`);

                    // 创建转录数据
                    const transcriptionData = {
                      type: "transcription",
                      text: result.text.trim(),
                      confidence: result.confidence || 0.9,
                      isFinal: result.isFinal || false,
                      timestamp: Date.now(),
                      participant: participant.identity,
                    };

                    // 广播转录结果
                    ctx.room.localParticipant
                      .publishData(JSON.stringify(transcriptionData), {
                        reliable: true,
                      })
                      .catch((err) =>
                        console.error("❌ 广播失败:", err.message)
                      );
                  }
                });

                sttStream.on("error", (error) => {
                  console.error(`❌ STT 错误: ${error.message}`);
                });
              } else {
                console.log(`🔗 使用流式 API 连接音频轨道到 STT...`);

                // 使用 LiveKit Agents 的流式 API
                try {
                  console.log(
                    `🔍 STT Input 类型: ${typeof sttStream.input}, 方法: ${Object.getOwnPropertyNames(
                      sttStream.input
                    )}`
                  );
                  console.log(
                    `🔍 STT Output 类型: ${typeof sttStream.output}, 方法: ${Object.getOwnPropertyNames(
                      sttStream.output
                    )}`
                  );

                  // 尝试不同的连接方法
                  if (sttStream.input.connectTrack) {
                    sttStream.input.connectTrack(track);
                  } else if (sttStream.input.connect) {
                    sttStream.input.connect(track);
                  } else {
                    console.error(
                      `❌ 无法找到连接方法，可用方法: ${Object.getOwnPropertyNames(
                        sttStream.input
                      )}`
                    );
                    return;
                  }

                  // 处理 STT 输出
                  const handleSTTOutput = async () => {
                    try {
                      for await (const event of sttStream.output) {
                        if (
                          event.type === "transcript" &&
                          event.text &&
                          event.text.trim()
                        ) {
                          console.log(`📝 转录: ${event.text}`);

                          // 创建转录数据
                          const transcriptionData = {
                            type: "transcription",
                            text: event.text.trim(),
                            confidence: event.confidence || 0.9,
                            isFinal: event.isFinal || false,
                            timestamp: Date.now(),
                            participant: participant.identity,
                          };

                          // 广播转录结果
                          await ctx.room.localParticipant
                            .publishData(JSON.stringify(transcriptionData), {
                              reliable: true,
                            })
                            .catch((err) =>
                              console.error("❌ 广播失败:", err.message)
                            );
                        }
                      }
                    } catch (error) {
                      console.error(`❌ STT 输出处理错误: ${error.message}`);
                    }
                  };

                  // 启动 STT 输出处理
                  handleSTTOutput();

                  console.log("✅ 音频流已连接到 STT");
                } catch (error) {
                  console.error(`❌ 流式 API 连接失败: ${error.message}`);
                }
              }
            } catch (error) {
              console.error(`❌ 处理音频轨道失败: ${error.message}`);
            }
          }
        }
      );

      console.log("🎯 Agent 设置完成，等待音频...");
    } catch (error) {
      console.error("❌ Agent 启动失败:", error.message);
      console.error("错误详情:", error.stack);
      throw error;
    }
  },
});

// 启动 Agent
console.log("🔧 启动 LiveKit Worker...");

try {
  const workerOptions = new WorkerOptions({
    agent: fileURLToPath(import.meta.url),
    logLevel: "info",
  });

  cli.runApp(workerOptions);
} catch (error) {
  console.error("❌ Worker 启动失败:", error.message);
  process.exit(1);
}

export default transcriptionAgent;
