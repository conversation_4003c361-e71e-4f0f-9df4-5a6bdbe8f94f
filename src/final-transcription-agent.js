#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 最终转录 Agent
 * 基于官方文档的最简化但正确的实现
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动最终转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动最终转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 创建 STT 流
    const sttStream = stt.stream();
    console.log("🔍 STT Stream 已创建");

    // 处理 STT 输出
    (async () => {
      try {
        for await (const event of sttStream) {
          console.log(`📡 STT 事件: ${JSON.stringify(event)}`);
          
          if (event.text && event.text.trim()) {
            console.log(`📝 转录: ${event.text} (isFinal: ${event.isFinal})`);

            // 创建转录数据
            const transcriptionData = {
              type: "transcription",
              text: event.text.trim(),
              confidence: event.confidence || 0.9,
              isFinal: event.isFinal || false,
              timestamp: Date.now(),
              participant: participant.identity,
            };

            // 广播转录结果到房间
            try {
              await ctx.room.localParticipant.publishData(
                JSON.stringify(transcriptionData),
                { reliable: true }
              );
              console.log(`✅ 转录结果已广播: ${event.text}`);
            } catch (error) {
              console.error(`❌ 广播转录结果失败: ${error.message}`);
            }
          }
        }
      } catch (error) {
        console.error(`❌ STT 流处理错误: ${error.message}`);
      }
    })();

    // 监听房间的轨道订阅事件
    ctx.room.on("trackSubscribed", async (track, publication, participant) => {
      console.log(`🎵 轨道订阅: ${track.kind} by ${participant.identity}`);
      
      if (track.kind === "audio" || track.kind === 1) {
        console.log(`🎧 开始处理音频轨道: ${participant.identity}`);
        
        try {
          // 根据文档，我们需要将音频数据推送到 STT 流
          // 由于 LiveKit Agents JS 还在 beta，API 可能不完整
          // 让我们尝试直接使用 STT 对象处理音频
          
          console.log(`🔗 尝试使用 STT 直接处理音频...`);
          
          // 检查 STT 对象的方法
          console.log(`🔍 STT 对象方法: ${Object.getOwnPropertyNames(stt)}`);
          
          // 尝试使用 STT 的 transcribe 方法（如果存在）
          if (typeof stt.transcribe === 'function') {
            console.log(`🎯 使用 stt.transcribe 方法`);
            try {
              const result = await stt.transcribe(track);
              console.log(`📝 直接转录结果: ${JSON.stringify(result)}`);
            } catch (error) {
              console.error(`❌ 直接转录失败: ${error.message}`);
            }
          } else {
            console.log(`⚠️ stt.transcribe 方法不存在`);
          }

        } catch (error) {
          console.error(`❌ 处理音频轨道失败: ${error.message}`);
        }
      }
    });

    console.log("🎯 最终转录 Agent 已启动，等待音频输入...");
    
    // 保持 Agent 运行
    await new Promise(() => {}); // 永远等待
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
