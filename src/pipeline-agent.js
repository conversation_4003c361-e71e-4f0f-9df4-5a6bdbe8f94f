#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 流水线 Agent
 * 基于官方文档的正确 STT-LLM-TTS 流水线架构
 */

import { WorkerOptions, cli, defineAgent, AgentSession } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import * as openai from "@livekit/agents-plugin-openai";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动流水线转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
  "OPENAI_API_KEY", // 需要 OpenAI 用于 LLM，即使我们主要做转录
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动流水线转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 AgentSession，使用标准的 STT-LLM-TTS 流水线
    const session = new AgentSession({
      // STT 配置
      stt: new deepgram.STT({
        model: "nova-2",
        language: "multi",
        smartFormat: true,
        interimResults: true,
        punctuate: true,
        diarize: true,
      }),
      
      // LLM 配置（用于处理转录结果）
      llm: new openai.LLM({
        model: "gpt-4o-mini",
      }),
      
      // 不使用 TTS，因为我们只做转录
      // tts: null,
    });

    console.log("🎤 AgentSession 已配置");

    // 启动会话
    await session.start({
      room: ctx.room,
      participant: participant,
    });

    console.log("✅ AgentSession 已启动");

    // 监听用户语音转录完成事件
    session.on("user_speech_committed", async (event) => {
      console.log(`📝 用户语音转录完成: ${event.text}`);
      
      // 创建转录数据
      const transcriptionData = {
        type: "transcription",
        text: event.text,
        confidence: event.confidence || 0.9,
        isFinal: true,
        timestamp: Date.now(),
        participant: participant.identity,
      };

      // 广播转录结果到房间
      try {
        await ctx.room.localParticipant.publishData(
          JSON.stringify(transcriptionData),
          { reliable: true }
        );
        console.log(`✅ 转录结果已广播: ${event.text}`);
      } catch (error) {
        console.error(`❌ 广播转录结果失败: ${error.message}`);
      }
    });

    // 监听中间转录结果
    session.on("user_speech_interim", async (event) => {
      if (event.text && event.text.trim()) {
        console.log(`📝 中间转录: ${event.text}`);
        
        // 创建中间转录数据
        const transcriptionData = {
          type: "transcription",
          text: event.text,
          confidence: event.confidence || 0.8,
          isFinal: false,
          timestamp: Date.now(),
          participant: participant.identity,
        };

        // 广播中间转录结果
        try {
          await ctx.room.localParticipant.publishData(
            JSON.stringify(transcriptionData),
            { reliable: true }
          );
          console.log(`📡 中间转录已广播: ${event.text}`);
        } catch (error) {
          console.error(`❌ 广播中间转录失败: ${error.message}`);
        }
      }
    });

    // 监听会话错误
    session.on("error", (error) => {
      console.error(`❌ AgentSession 错误: ${error.message}`);
    });

    console.log("🎯 流水线转录 Agent 已启动，等待语音输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
