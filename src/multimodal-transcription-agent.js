#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 多模态转录 Agent
 * 基于官方 multimodal_agent.ts 示例的正确实现
 */

import { WorkerOptions, cli, defineAgent, multimodal } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import * as openai from "@livekit/agents-plugin-openai";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动多模态转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
  "OPENAI_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动多模态转录 Agent...");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 创建 OpenAI LLM（用于处理转录结果）
    const llm = new openai.LLM({
      model: "gpt-4o-mini",
    });

    console.log("🧠 OpenAI LLM 已初始化");

    // 创建 OpenAI TTS（即使我们不使用，也可能需要它来避免错误）
    const tts = new openai.TTS({
      model: "tts-1",
      voice: "alloy",
    });

    console.log("🔊 OpenAI TTS 已初始化（备用）");

    // 创建多模态 Agent - 这是关键！
    const agent = new multimodal.MultimodalAgent({
      stt: stt,
      llm: llm,
      tts: tts, // 添加 TTS 以避免配置错误
    });

    console.log("🤖 MultimodalAgent 已创建");

    // 启动 Agent - 这会自动处理音频流连接！
    const session = await agent.start(ctx.room, participant);

    console.log("✅ MultimodalAgent 会话已启动，自动处理音频流");

    // 监听用户语音转录事件
    session.on("user_speech_committed", async (event) => {
      console.log(`📝 用户语音转录完成: ${event.text}`);

      // 创建转录数据
      const transcriptionData = {
        type: "transcription",
        text: event.text,
        confidence: event.confidence || 0.9,
        isFinal: true,
        timestamp: Date.now(),
        participant: participant.identity,
      };

      // 广播转录结果到房间
      try {
        await ctx.room.localParticipant.publishData(
          JSON.stringify(transcriptionData),
          { reliable: true }
        );
        console.log(`✅ 转录结果已广播: ${event.text}`);
      } catch (error) {
        console.error(`❌ 广播转录结果失败: ${error.message}`);
      }
    });

    // 监听中间转录结果
    session.on("user_speech_interim", async (event) => {
      if (event.text && event.text.trim()) {
        console.log(`📝 中间转录: ${event.text}`);

        // 创建中间转录数据
        const transcriptionData = {
          type: "transcription",
          text: event.text,
          confidence: event.confidence || 0.8,
          isFinal: false,
          timestamp: Date.now(),
          participant: participant.identity,
        };

        // 广播中间转录结果
        try {
          await ctx.room.localParticipant.publishData(
            JSON.stringify(transcriptionData),
            { reliable: true }
          );
          console.log(`📡 中间转录已广播: ${event.text}`);
        } catch (error) {
          console.error(`❌ 广播中间转录失败: ${error.message}`);
        }
      }
    });

    // 监听 Agent 响应事件
    session.on("agent_speech_committed", async (event) => {
      console.log(`🤖 Agent 响应: ${event.text}`);
    });

    // 监听会话错误
    session.on("error", (error) => {
      console.error(`❌ MultimodalAgent 会话错误: ${error.message}`);
    });

    // 发送初始问候消息
    try {
      console.log("👋 发送初始问候消息...");

      // 使用 session 发送初始消息
      await session.say(
        "你好！我是语音转录助手，我会将你的语音转换为文字。请开始说话。",
        { allowInterruptions: false }
      );

      console.log("✅ 初始问候消息已发送");
    } catch (error) {
      console.error(`❌ 发送初始问候失败: ${error.message}`);
    }

    console.log("🎯 多模态转录 Agent 已完全启动，等待语音输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
