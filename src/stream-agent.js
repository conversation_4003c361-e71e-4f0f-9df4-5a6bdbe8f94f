#!/usr/bin/env node

/**
 * LiveKit Node.js Agents + Deepgram 流式 Agent
 * 使用正确的流式处理方法
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 环境变量检查
console.log("🚀 启动流式转录 Agent...");

const requiredEnvVars = [
  "LIVEKIT_URL",
  "LIVEKIT_API_KEY",
  "LIVEKIT_API_SECRET",
  "DEEPGRAM_API_KEY",
];

const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);
if (missingVars.length > 0) {
  console.error("❌ 缺少环境变量:", missingVars.join(", "));
  process.exit(1);
}

console.log("✅ 环境变量检查通过");

// 定义 Agent
export default defineAgent({
  entry: async (ctx) => {
    console.log("🔧 启动流式转录 Agent...");
    
    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);
    
    // 等待参与者加入
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者已加入: ${participant.identity}`);

    // 创建 Deepgram STT
    const stt = new deepgram.STT({
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    });

    console.log("🎤 Deepgram STT 已初始化");

    // 监听房间的轨道订阅事件
    ctx.room.on("trackSubscribed", async (track, publication, participant) => {
      console.log(`🎵 轨道订阅: ${track.kind} by ${participant.identity}`);
      
      if (track.kind === "audio" || track.kind === 1) {
        console.log(`🎧 开始处理音频轨道: ${participant.identity}`);
        
        try {
          // 创建 STT 流
          const sttStream = stt.stream();
          console.log(`🔍 STT Stream 已创建`);

          // 尝试使用流式 API 的正确方法
          console.log(`🔗 尝试使用流式 API...`);
          
          // 方法1: 尝试使用 async iterator 直接处理轨道
          try {
            console.log(`🎯 方法1: 直接处理音频轨道流...`);
            
            // 检查轨道是否有 async iterator
            if (track[Symbol.asyncIterator]) {
              console.log(`✅ 轨道支持 async iterator`);
              
              // 处理音频帧
              for await (const frame of track) {
                console.log(`🎵 收到音频帧: ${frame.length} bytes`);
                
                // 将音频帧推送到 STT
                if (sttStream.input && typeof sttStream.input.write === 'function') {
                  sttStream.input.write(frame);
                } else if (sttStream.input && typeof sttStream.input.push === 'function') {
                  sttStream.input.push(frame);
                }
              }
            } else {
              console.log(`❌ 轨道不支持 async iterator`);
            }
          } catch (error) {
            console.log(`❌ 方法1 失败: ${error.message}`);
          }

          // 方法2: 尝试使用事件监听
          try {
            console.log(`🎯 方法2: 使用事件监听...`);
            
            if (typeof track.on === 'function') {
              track.on('data', (audioData) => {
                console.log(`🎵 收到音频数据: ${audioData.length} bytes`);
                
                // 将音频数据推送到 STT
                if (sttStream.input) {
                  if (typeof sttStream.input.write === 'function') {
                    sttStream.input.write(audioData);
                  } else if (typeof sttStream.input.push === 'function') {
                    sttStream.input.push(audioData);
                  }
                }
              });
              
              console.log(`✅ 音频数据事件监听已设置`);
            } else {
              console.log(`❌ 轨道不支持事件监听`);
            }
          } catch (error) {
            console.log(`❌ 方法2 失败: ${error.message}`);
          }

          // 方法3: 尝试直接使用 STT 处理轨道
          try {
            console.log(`🎯 方法3: STT 直接处理轨道...`);
            
            // 检查 STT 是否有处理轨道的方法
            const sttMethods = Object.getOwnPropertyNames(stt);
            console.log(`🔍 STT 方法: ${sttMethods}`);
            
            if (typeof stt.processTrack === 'function') {
              console.log(`✅ 使用 stt.processTrack`);
              await stt.processTrack(track);
            } else if (typeof stt.transcribeTrack === 'function') {
              console.log(`✅ 使用 stt.transcribeTrack`);
              await stt.transcribeTrack(track);
            } else {
              console.log(`❌ STT 没有轨道处理方法`);
            }
          } catch (error) {
            console.log(`❌ 方法3 失败: ${error.message}`);
          }

          // 处理 STT 输出
          (async () => {
            try {
              console.log(`🎧 开始监听 STT 输出...`);
              
              for await (const event of sttStream) {
                console.log(`📡 STT 事件: ${JSON.stringify(event)}`);
                
                if (event.text && event.text.trim()) {
                  console.log(`📝 转录: ${event.text} (isFinal: ${event.isFinal})`);

                  // 创建转录数据
                  const transcriptionData = {
                    type: "transcription",
                    text: event.text.trim(),
                    confidence: event.confidence || 0.9,
                    isFinal: event.isFinal || false,
                    timestamp: Date.now(),
                    participant: participant.identity,
                  };

                  // 广播转录结果到房间
                  try {
                    await ctx.room.localParticipant.publishData(
                      JSON.stringify(transcriptionData),
                      { reliable: true }
                    );
                    console.log(`✅ 转录结果已广播: ${event.text}`);
                  } catch (error) {
                    console.error(`❌ 广播转录结果失败: ${error.message}`);
                  }
                }
              }
            } catch (error) {
              console.error(`❌ STT 输出处理错误: ${error.message}`);
            }
          })();

        } catch (error) {
          console.error(`❌ 处理音频轨道失败: ${error.message}`);
        }
      }
    });

    console.log("🎯 流式转录 Agent 已启动，等待音频输入...");
  },
});

// 启动 Worker
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
