# LiveKit Agents + Deepgram 实时语音转录

这是一个基于 LiveKit Agents 和 Deepgram 的企业级实时语音转录应用。采用 Node.js Agent 架构，通过 LiveKit 房间进行音频传输，使用 Deepgram STT 进行高质量语音识别。

## ✨ 功能特性

- 🎤 **实时语音转录**: 使用 Deepgram Nova-2/Nova-3 模型进行高精度语音识别
- � **多人对话支持**: 支持多参与者同时语音转录
- 🎯 **说话人分离**: 自动识别不同说话人并分类显示
- �📝 **实时显示**: 显示临时转录结果和最终转录文本
- 🌐 **Web 界面**: 现代化的 ChatUI 风格界面
- � **多语言支持**: 支持中英文等多语言自动识别
- � **会话管理**: 显示转录置信度、时间戳和会话统计
- 📱 **响应式设计**: 适配桌面和移动设备

## 🏗️ 架构设计

```
浏览器 → LiveKit Client → LiveKit 服务器
                              ↓
Node.js Agent → 监听房间 → 获取音频 → Deepgram STT → 返回转录结果
                              ↑
浏览器 ← 接收转录结果 ← LiveKit 服务器
```

## 🛠️ 技术栈

- **LiveKit Agents**: Node.js Agent 处理音频流
- **语音识别**: Deepgram STT API (Nova-2/Nova-3)
- **前端**: HTML5 + JavaScript + LiveKit Client SDK
- **实时通信**: LiveKit WebRTC
- **后端**: Express.js 服务器

## 📋 前置要求

1. **Node.js**: 版本 18 或更高
2. **LiveKit 服务器**: 可以使用 LiveKit Cloud 或自建服务器
3. **Deepgram API 密钥**: 从 [Deepgram](https://deepgram.com/) 获取

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <your-repo-url>
cd livekit-deepgram-transcription
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

创建 `.env` 文件并填入你的配置：

```env
# LiveKit 配置
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Deepgram 配置 (Agent 使用)
DEEPGRAM_API_KEY=your-deepgram-api-key

# 服务器配置
PORT=3000
```

#### LiveKit 配置选项

**选项 1: 使用 LiveKit Cloud (推荐)**

1. 访问 [LiveKit Cloud](https://cloud.livekit.io/)
2. 创建项目并获取 API 密钥
3. 使用格式: `wss://your-project.livekit.cloud`

**选项 2: 自建 LiveKit 服务器**

使用项目中的 Docker Compose 配置：

```bash
cd livekit-server
docker-compose up -d
```

### 4. 获取 Deepgram API 密钥

1. 访问 [Deepgram](https://deepgram.com/)
2. 注册账户并获取 API 密钥
3. 将密钥添加到 `.env` 文件

### 5. 启动应用

**推荐方式: 分别启动**

```bash
# 终端 1: 启动 Web 服务器
npm start

# 终端 2: 启动 Node.js Agent
node src/transcription-agent.js
```

### 6. 访问应用

打开浏览器访问: `http://localhost:3000`

## 📖 使用说明

1. **启动 Agent**: 确保 Node.js Agent 正在运行
2. **连接房间**: 点击"开始转录"按钮
3. **授权麦克风**: 浏览器会请求麦克风权限，请允许
4. **开始说话**: 连接成功后，开始说话即可看到实时转录
5. **查看结果**:
   - 实时显示临时转录结果
   - 最终转录结果会显示在聊天界面
   - 支持多人对话和说话人分离

## 🎯 项目结构

```
├── src/
│   ├── agent.js                 # 简单测试 Agent
│   └── transcription-agent.js   # 完整转录 Agent (推荐)
├── public/
│   ├── index.html              # 前端界面
│   └── app.js                  # 前端 JavaScript
├── livekit-server/             # LiveKit 服务器配置
│   ├── docker-compose.yml     # Docker 部署配置
│   └── livekit.yaml           # LiveKit 配置文件
├── server.js                   # Express 服务器
├── package.json               # 项目配置
└── README.md                  # 项目文档
```

## 🔧 开发命令

```bash
# 启动服务
npm start                      # 启动 Web 服务器
node src/transcription-agent.js  # 启动转录 Agent

# 开发模式
npm run dev                    # 启动 Web 服务器 (开发模式)

# 健康检查
curl http://localhost:3000/api/health  # 检查服务器状态
```

## 🐛 故障排除

### 常见问题

1. **Agent 连接失败**

   - 检查 LiveKit 服务器是否运行
   - 验证 API 密钥和密钥是否正确
   - 确保 Deepgram API 密钥有效
   - 检查 Agent 终端输出的错误信息

2. **浏览器连接失败**

   - 检查 LiveKit 服务器是否可访问
   - 确保防火墙允许 WebSocket 连接
   - 验证浏览器支持 WebRTC

3. **麦克风权限被拒绝**

   - 确保浏览器允许麦克风访问
   - 在 HTTPS 环境下使用（生产环境）

4. **转录不工作**

   - 确保 Node.js Agent 正在运行
   - 检查 Deepgram API 密钥是否有效
   - 查看 Agent 终端的错误日志
   - 确保音频流正常传输到 Agent

5. **音频质量问题**
   - 检查麦克风设备
   - 确保环境噪音较小
   - 调整 Deepgram 模型参数（在 Agent 代码中）

### 调试技巧

1. **查看 Agent 日志**:

   ```bash
   node src/transcription-agent.js  # 查看详细日志
   ```

2. **查看浏览器控制台**: 打开开发者工具查看 LiveKit 连接状态

3. **检查服务状态**: 访问 `http://localhost:3000/api/health`

4. **测试 LiveKit 连接**: 确保能正常连接到 LiveKit 服务器

## 🔒 安全注意事项

1. **API 密钥安全**:

   - 不要将 `.env` 文件提交到版本控制
   - 在生产环境中使用环境变量

2. **访问控制**:

   - 考虑添加用户认证
   - 限制房间访问权限

3. **HTTPS**:
   - 生产环境必须使用 HTTPS
   - 麦克风访问需要安全上下文

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 [LiveKit 文档](https://docs.livekit.io/)
3. 查看 [Deepgram 文档](https://developers.deepgram.com/)
4. 提交 Issue 到项目仓库

---

**享受实时语音转文本的体验！** 🎉
