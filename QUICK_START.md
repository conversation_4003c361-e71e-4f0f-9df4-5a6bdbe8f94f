# 🚀 LiveKit Agents + Deepgram 转录系统快速开始指南

## 📋 前置要求

### 1. 环境要求
- Node.js 18+ 
- npm 或 pnpm
- LiveKit Cloud 账户或自托管 LiveKit 服务器
- Deepgram API 密钥
- OpenAI API 密钥（推荐）

### 2. 账户设置
1. **LiveKit Cloud**: 
   - 注册 [LiveKit Cloud](https://cloud.livekit.io/)
   - 获取 API Key 和 Secret
   
2. **Deepgram**:
   - 注册 [Deepgram](https://console.deepgram.com/)
   - 获取 API Key
   
3. **OpenAI** (可选):
   - 注册 [OpenAI](https://platform.openai.com/)
   - 获取 API Key

## ⚡ 快速安装

### 1. 克隆项目
```bash
git clone <your-repo>
cd livekit_deepg
```

### 2. 安装依赖
```bash
npm install
# 或
pnpm install
```

### 3. 环境配置
复制并编辑环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# LiveKit 配置
LIVEKIT_URL=wss://your-livekit-server.livekit.cloud
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Deepgram 配置
DEEPGRAM_API_KEY=your-deepgram-api-key

# OpenAI 配置 (推荐)
OPENAI_API_KEY=your-openai-api-key

# 服务器配置
PORT=5000
```

## 🎯 推荐使用方式

### 方案 1: MultimodalAgent (推荐)
使用基于官方示例的完整实现：

```bash
# 启动 Agent
node src/multimodal-transcription-agent.js start

# 开发模式
node src/multimodal-transcription-agent.js dev

# 连接特定房间
node src/multimodal-transcription-agent.js connect --room my-room
```

### 方案 2: 纯转录 Agent
如果不需要 LLM 功能：

```bash
node src/transcription-only-agent.js start
```

### 方案 3: 调试版本
用于深度调试和 API 探索：

```bash
node src/working-agent.js start
```

## 🌐 启动前端界面

### 1. 启动 Web 服务器
```bash
# 在另一个终端中
node server.js
```

### 2. 访问界面
打开浏览器访问: `http://localhost:5000`

## 📱 使用流程

### 1. 启动系统
1. 启动 Agent: `node src/multimodal-transcription-agent.js start`
2. 启动 Web 服务器: `node server.js`
3. 打开浏览器: `http://localhost:5000`

### 2. 开始转录
1. 在浏览器中点击"开始录音"
2. 允许麦克风权限
3. 开始说话
4. 查看实时转录结果

### 3. 查看日志
Agent 终端会显示详细的处理日志：
```
🚀 启动多模态转录 Agent...
✅ 环境变量检查通过
📍 已连接到房间: transcription-room
👤 参与者已加入: user-xxx
🎤 Deepgram STT 已初始化
🤖 MultimodalAgent 已创建
📝 转录: Hello, how are you today?
✅ 转录结果已广播
```

## 🔧 配置选项

### Deepgram STT 配置
```javascript
const stt = new deepgram.STT({
  model: "nova-2",        // 推荐: nova-2 或 nova-3
  language: "multi",      // 多语言支持
  smartFormat: true,      // 智能格式化
  interimResults: true,   // 实时结果
  punctuate: true,        // 自动标点
  diarize: true,         // 说话人分离
});
```

### OpenAI LLM 配置
```javascript
const llm = new openai.LLM({
  model: "gpt-4o-mini",   // 推荐模型
});
```

### OpenAI TTS 配置
```javascript
const tts = new openai.TTS({
  model: "tts-1",         // TTS 模型
  voice: "alloy",         // 语音选择
});
```

## 🐛 故障排除

### 常见问题

#### 1. sampleRate 错误
```
Error: Cannot read properties of undefined (reading 'sampleRate')
```
**解决方案**: 这是 LiveKit Agents JS beta 版本的已知问题，确保配置了完整的 STT-LLM-TTS。

#### 2. 连接失败
```
Error: Failed to connect to LiveKit server
```
**检查**:
- LiveKit URL 是否正确
- API Key 和 Secret 是否有效
- 网络连接是否正常

#### 3. Deepgram API 错误
```
Error: Deepgram API authentication failed
```
**检查**:
- Deepgram API Key 是否正确
- 账户是否有足够的额度

#### 4. 麦克风权限
**浏览器端**:
- 确保允许麦克风权限
- 使用 HTTPS 或 localhost
- 检查浏览器兼容性

### 调试技巧

#### 1. 启用详细日志
```bash
# 使用 dev 模式获得更多日志
node src/multimodal-transcription-agent.js dev
```

#### 2. 检查环境变量
```bash
# 验证环境变量
node -e "console.log(process.env.LIVEKIT_URL)"
```

#### 3. 测试连接
```bash
# 使用 LiveKit CLI 测试连接
livekit-cli token create --room test-room --identity test-user
```

## 📊 监控和日志

### 1. Agent 日志
Agent 会输出详细的处理日志：
- 🚀 启动信息
- 📍 连接状态
- 👤 参与者事件
- 🎤 音频处理
- 📝 转录结果
- ❌ 错误信息

### 2. 性能监控
```javascript
// 添加性能监控
console.time('transcription-processing');
// ... 处理逻辑
console.timeEnd('transcription-processing');
```

### 3. 错误追踪
```javascript
// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
});
```

## 🔄 版本选择指南

### 生产环境推荐
```bash
# 使用 MultimodalAgent (最稳定)
node src/multimodal-transcription-agent.js start
```

### 开发调试推荐
```bash
# 使用 working-agent (详细日志)
node src/working-agent.js dev
```

### 轻量级部署推荐
```bash
# 使用纯转录版本 (最小依赖)
node src/transcription-only-agent.js start
```

## 📈 扩展部署

### Docker 部署 (推荐)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 5000
CMD ["node", "src/multimodal-transcription-agent.js", "start"]
```

### PM2 部署
```bash
# 安装 PM2
npm install -g pm2

# 启动 Agent
pm2 start src/multimodal-transcription-agent.js --name "transcription-agent"

# 启动 Web 服务器
pm2 start server.js --name "web-server"
```

### Kubernetes 部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: transcription-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: transcription-agent
  template:
    metadata:
      labels:
        app: transcription-agent
    spec:
      containers:
      - name: agent
        image: your-registry/transcription-agent:latest
        env:
        - name: LIVEKIT_URL
          valueFrom:
            secretKeyRef:
              name: livekit-secrets
              key: url
```

## 🎯 下一步

1. **等待 LiveKit Agents JS 正式版**
   - 关注 [GitHub 仓库](https://github.com/livekit/agents-js)
   - 订阅更新通知

2. **功能增强**
   - 添加多语言支持
   - 实现转录历史记录
   - 添加用户界面优化

3. **生产部署**
   - 配置监控系统
   - 设置自动扩缩容
   - 实现高可用部署

## 📞 支持

- **项目文档**: 查看 `PROJECT_SUMMARY.md`
- **技术细节**: 查看 `TECHNICAL_DETAILS.md`
- **问题反馈**: 创建 GitHub Issue
- **社区支持**: [LiveKit Slack](https://livekit.io/join-slack)

---

**快速开始指南版本**: v1.0  
**最后更新**: 2025年1月  
**适用版本**: LiveKit Agents JS Beta
